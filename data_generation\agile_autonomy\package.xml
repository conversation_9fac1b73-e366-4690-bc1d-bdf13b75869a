<?xml version="1.0"?>
<package format="2">
  <name>agile_autonomy</name>
  <version>0.0.0</version>
  <description>The agile_autonomy package</description>

  <maintainer email="e<PERSON><PERSON><PERSON>@ifi.uzh.ch"><PERSON><PERSON></maintainer>
  <license>TODO</license>

  <author><PERSON><PERSON></author>

  <buildtool_depend>catkin</buildtool_depend>
  <buildtool_depend>catkin_simple</buildtool_depend>

  <depend>autopilot</depend>
  <depend>cv_bridge</depend>
  <depend>eigen_catkin</depend>
  <depend>gazebo_msgs</depend>
  <depend>glog_catkin</depend>
  <depend>minimum_jerk_trajectories</depend>
  <depend>agile_autonomy_msgs</depend>
  <depend>agile_autonomy_utils</depend>
  <depend>quadrotor_common</depend>
  <depend>quadrotor_msgs</depend>
  <depend>roscpp</depend>
  <depend>rpg_common</depend>
  <depend>rpg_mpc</depend>
  <depend>sensor_msgs</depend>
  <depend>state_predictor</depend>
  <depend>std_msgs</depend>
  <depend>nav_msgs</depend>
  <depend>rpgq_simulator</depend>

  <export>

  </export>
</package>