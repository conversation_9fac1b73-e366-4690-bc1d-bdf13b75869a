Panels:
  - Class: rviz/Displays
    Help Height: 0
    Name: Displays
    Property Tree Widget:
      Expanded:
        - /Trajectories1
      Splitter Ratio: 0.5346534848213196
    Tree Height: 322
  - Class: rviz/Selection
    Name: Selection
  - Class: rviz/Tool Properties
    Expanded:
      - /2D Pose Estimate1
      - /2D Nav Goal1
      - /Publish Point1
    Name: Tool Properties
    Splitter Ratio: 0.5886790156364441
  - Class: rviz/Views
    Expanded:
      - /Current View1
    Name: Views
    Splitter Ratio: 0.5
  - Class: rviz/Time
    Experimental: false
    Name: Time
    SyncMode: 0
    SyncSource: RGB
Preferences:
  PromptSaveOnExit: true
Toolbars:
  toolButtonStyle: 2
Visualization Manager:
  Class: ""
  Displays:
    - Class: rviz/Group
      Displays:
        - Alpha: 0.5
          Cell Size: 1
          Class: rviz/Grid
          Color: 160; 160; 164
          Enabled: true
          Line Style:
            Line Width: 0.029999999329447746
            Value: Lines
          Name: Grid
          Normal Cell Count: 0
          Offset:
            X: 0
            Y: 0
            Z: 0
          Plane: XY
          Plane Cell Count: 100
          Reference Frame: <Fixed Frame>
          Value: true
        - Angle Tolerance: 0
          Class: rviz/Odometry
          Covariance:
            Orientation:
              Alpha: 0.5
              Color: 255; 255; 127
              Color Style: Unique
              Frame: Local
              Offset: 1
              Scale: 1
              Value: true
            Position:
              Alpha: 0.30000001192092896
              Color: 204; 51; 204
              Scale: 1
              Value: true
            Value: true
          Enabled: true
          Keep: 1
          Name: GT Quadrotor Pose
          Position Tolerance: 0
          Queue Size: 10
          Shape:
            Alpha: 1
            Axes Length: 0.20000000298023224
            Axes Radius: 0.05000000074505806
            Color: 255; 25; 0
            Head Length: 0.30000001192092896
            Head Radius: 0.10000000149011612
            Shaft Length: 1
            Shaft Radius: 0.05000000074505806
            Value: Axes
          Topic: /hummingbird/ground_truth/odometry
          Unreliable: false
          Value: true
        - Class: rviz/MarkerArray
          Enabled: false
          Marker Topic: /hummingbird/quadrotor_viz
          Name: Quadrotor Body
          Namespaces:
            {}
          Queue Size: 100
          Value: false
      Enabled: true
      Name: Global Stuff
    - Class: rviz/Group
      Displays:
        - Alpha: 1
          Buffer Length: 1
          Class: rviz/Path
          Color: 252; 233; 79
          Enabled: false
          Head Diameter: 0.30000001192092896
          Head Length: 0.20000000298023224
          Length: 0.30000001192092896
          Line Style: Lines
          Line Width: 0.029999999329447746
          Name: MPC Path
          Offset:
            X: 0
            Y: 0
            Z: 0
          Pose Color: 255; 85; 255
          Pose Style: Axes
          Queue Size: 10
          Radius: 0.029999999329447746
          Shaft Diameter: 0.10000000149011612
          Shaft Length: 0.10000000149011612
          Topic: /hummingbird/vio_mpc_path
          Unreliable: false
          Value: false
        - Class: rviz/MarkerArray
          Enabled: false
          Marker Topic: /hummingbird/active_reference
          Name: Active Reference
          Namespaces:
            {}
          Queue Size: 100
          Value: false
        - Alpha: 1
          Buffer Length: 1
          Class: rviz/Path
          Color: 25; 255; 0
          Enabled: false
          Head Diameter: 0.30000001192092896
          Head Length: 0.20000000298023224
          Length: 0.30000001192092896
          Line Style: Lines
          Line Width: 0.029999999329447746
          Name: Control Reference
          Offset:
            X: 0
            Y: 0
            Z: 0
          Pose Color: 255; 85; 255
          Pose Style: Axes
          Queue Size: 10
          Radius: 0.029999999329447746
          Shaft Diameter: 0.10000000149011612
          Shaft Length: 0.10000000149011612
          Topic: /hummingbird/path/control_ref
          Unreliable: false
          Value: false
        - Alpha: 1
          Buffer Length: 1
          Class: rviz/Path
          Color: 245; 121; 0
          Enabled: true
          Head Diameter: 0.30000001192092896
          Head Length: 0.20000000298023224
          Length: 0.30000001192092896
          Line Style: Lines
          Line Width: 0.029999999329447746
          Name: Executed Reference
          Offset:
            X: 0
            Y: 0
            Z: 0
          Pose Color: 255; 85; 255
          Pose Style: None
          Queue Size: 10
          Radius: 0.029999999329447746
          Shaft Diameter: 0.10000000149011612
          Shaft Length: 0.10000000149011612
          Topic: /hummingbird/agile_autonomy/path/executed_reference
          Unreliable: false
          Value: true
        - Alpha: 1
          Buffer Length: 1
          Class: rviz/Path
          Color: 78; 154; 6
          Enabled: true
          Head Diameter: 0.30000001192092896
          Head Length: 0.20000000298023224
          Length: 0.30000001192092896
          Line Style: Lines
          Line Width: 0.029999999329447746
          Name: Executed Trajectory
          Offset:
            X: 0
            Y: 0
            Z: 0
          Pose Color: 255; 85; 255
          Pose Style: None
          Queue Size: 10
          Radius: 0.029999999329447746
          Shaft Diameter: 0.10000000149011612
          Shaft Length: 0.10000000149011612
          Topic: /hummingbird/agile_autonomy/path/executed_trajectory
          Unreliable: false
          Value: true
        - Class: rviz/MarkerArray
          Enabled: false
          Marker Topic: /hummingbird/agile_autonomy/trajectory/nominal_reference
          Name: Nominal Reference
          Namespaces:
            {}
          Queue Size: 100
          Value: false
        - Class: rviz/MarkerArray
          Enabled: true
          Marker Topic: /hummingbird/agile_autonomy/trajectory/fitted_reference
          Name: Fitted Reference
          Namespaces:
            "": true
          Queue Size: 100
          Value: true
      Enabled: true
      Name: Controller
    - Class: rviz/Group
      Displays:
        - Class: rviz/Image
          Enabled: true
          Image Topic: /hummingbird/agile_autonomy/unity_rgb
          Max Value: 1
          Median window: 5
          Min Value: 0
          Name: RGB
          Normalize Range: true
          Queue Size: 2
          Transport Hint: raw
          Unreliable: false
          Value: true
        - Class: rviz/Image
          Enabled: false
          Image Topic: /hummingbird/agile_autonomy/unity_depth
          Max Value: 1
          Median window: 5
          Min Value: 0
          Name: GT depth
          Normalize Range: true
          Queue Size: 2
          Transport Hint: raw
          Unreliable: false
          Value: false
        - Class: rviz/Image
          Enabled: false
          Image Topic: /hummingbird/agile_autonomy/sgm_depth
          Max Value: 1
          Median window: 5
          Min Value: 0
          Name: SGM depth
          Normalize Range: true
          Queue Size: 2
          Transport Hint: raw
          Unreliable: false
          Value: false
      Enabled: true
      Name: Images
    - Class: rviz/Group
      Displays:
        - Class: rviz/MarkerArray
          Enabled: false
          Marker Topic: /hummingbird/agile_autonomy/trajectory/raw_nw_prediction
          Name: Raw Trajectories
          Namespaces:
            {}
          Queue Size: 100
          Value: false
        - Class: rviz/MarkerArray
          Enabled: true
          Marker Topic: /hummingbird/agile_autonomy/trajectory/selected_nw_prediction
          Name: Selected Trajectory
          Namespaces:
            {}
          Queue Size: 100
          Value: true
        - Class: rviz/MarkerArray
          Enabled: true
          Marker Topic: /hummingbird/agile_autonomy/trajectory/fitted_nw_prediction
          Name: Fitted Trajectory
          Namespaces:
            {}
          Queue Size: 100
          Value: true
        - Class: rviz/MarkerArray
          Enabled: false
          Marker Topic: /hummingbird/agile_autonomy/trajectory/arclen_nw_prediction
          Name: Arclen Trajectory
          Namespaces:
            {}
          Queue Size: 100
          Value: false
        - Alpha: 1
          Buffer Length: 1
          Class: rviz/Path
          Color: 25; 255; 0
          Enabled: false
          Head Diameter: 0.30000001192092896
          Head Length: 0.20000000298023224
          Length: 0.30000001192092896
          Line Style: Lines
          Line Width: 0.029999999329447746
          Name: Raw Paths
          Offset:
            X: 0
            Y: 0
            Z: 0
          Pose Color: 255; 85; 255
          Pose Style: Axes
          Queue Size: 10
          Radius: 0.029999999329447746
          Shaft Diameter: 0.10000000149011612
          Shaft Length: 0.10000000149011612
          Topic: /hummingbird/agile_autonomy/path/raw_nw_prediction
          Unreliable: false
          Value: false
        - Alpha: 1
          Buffer Length: 1
          Class: rviz/Path
          Color: 25; 255; 0
          Enabled: false
          Head Diameter: 0.30000001192092896
          Head Length: 0.20000000298023224
          Length: 0.30000001192092896
          Line Style: Lines
          Line Width: 0.029999999329447746
          Name: Selected Path
          Offset:
            X: 0
            Y: 0
            Z: 0
          Pose Color: 255; 85; 255
          Pose Style: None
          Queue Size: 10
          Radius: 0.029999999329447746
          Shaft Diameter: 0.10000000149011612
          Shaft Length: 0.10000000149011612
          Topic: /hummingbird/agile_autonomy/path/selected_nw_prediction
          Unreliable: false
          Value: false
        - Alpha: 1
          Buffer Length: 1
          Class: rviz/Path
          Color: 25; 255; 0
          Enabled: false
          Head Diameter: 0.30000001192092896
          Head Length: 0.20000000298023224
          Length: 0.30000001192092896
          Line Style: Lines
          Line Width: 0.029999999329447746
          Name: Fitted Path
          Offset:
            X: 0
            Y: 0
            Z: 0
          Pose Color: 255; 85; 255
          Pose Style: Axes
          Queue Size: 10
          Radius: 0.029999999329447746
          Shaft Diameter: 0.10000000149011612
          Shaft Length: 0.10000000149011612
          Topic: /hummingbird/agile_autonomy/path/fitted_nw_prediction
          Unreliable: false
          Value: false
        - Alpha: 1
          Buffer Length: 1
          Class: rviz/Path
          Color: 25; 255; 0
          Enabled: false
          Head Diameter: 0.30000001192092896
          Head Length: 0.20000000298023224
          Length: 0.30000001192092896
          Line Style: Lines
          Line Width: 0.029999999329447746
          Name: Arclen Path
          Offset:
            X: 0
            Y: 0
            Z: 0
          Pose Color: 255; 85; 255
          Pose Style: Axes
          Queue Size: 10
          Radius: 0.029999999329447746
          Shaft Diameter: 0.10000000149011612
          Shaft Length: 0.10000000149011612
          Topic: /hummingbird/agile_autonomy/path/arclen_nw_prediction
          Unreliable: false
          Value: false
        - Class: rviz/MarkerArray
          Enabled: true
          Marker Topic: /hummingbird/agile_autonomy/planned_trajectory
          Name: Planned Trajectory
          Namespaces:
            "": true
          Queue Size: 100
          Value: true
      Enabled: true
      Name: Trajectories
    - Class: rviz/Marker
      Enabled: false
      Marker Topic: /hummingbird/agile_autonomy/pointcloud_viz
      Name: Pointcloud Viz
      Namespaces:
        {}
      Queue Size: 1
      Value: false
    - Class: rviz/Group
      Displays:
        - Alpha: 1
          Autocompute Intensity Bounds: true
          Autocompute Value Bounds:
            Max Value: 4.110620021820068
            Min Value: -0.8393809795379639
            Value: true
          Axis: Z
          Channel Name: intensity
          Class: rviz/PointCloud2
          Color: 255; 255; 255
          Color Transformer: AxisColor
          Decay Time: 0
          Enabled: false
          Invert Rainbow: false
          Max Color: 255; 255; 255
          Min Color: 0; 0; 0
          Name: PointCloud2
          Position Transformer: XYZ
          Queue Size: 10
          Selectable: true
          Size (Pixels): 3
          Size (m): 0.20000000298023224
          Style: Spheres
          Topic: /test_primitive/cloud
          Unreliable: false
          Use Fixed Frame: true
          Use rainbow: true
          Value: false
        - Alpha: 1
          Autocompute Intensity Bounds: true
          Autocompute Value Bounds:
            Max Value: 10
            Min Value: -10
            Value: true
          Axis: Z
          Channel Name: intensity
          Class: rviz/PointCloud
          Color: 255; 255; 255
          Color Transformer: ""
          Decay Time: 0
          Enabled: false
          Invert Rainbow: false
          Max Color: 255; 255; 255
          Min Color: 0; 0; 0
          Name: StartGoal
          Position Transformer: ""
          Queue Size: 10
          Selectable: true
          Size (Pixels): 3
          Size (m): 0.009999999776482582
          Style: Flat Squares
          Topic: /test_primitive/start_and_goal
          Unreliable: false
          Use Fixed Frame: true
          Use rainbow: true
          Value: false
        - Alpha: 0.5
          Class: decomp_rviz_plugins/EllipsoidArray
          Color: 204; 51; 204
          Enabled: false
          Name: EllipsoidArray
          Queue Size: 10
          Topic: /test_primitive/ellipsoids
          Unreliable: false
          Value: false
        - AccColor: 10; 200; 55
          AccScale: 0.019999999552965164
          AccVis: false
          Class: planning_rviz_plugins/Trajectory
          Enabled: true
          JrkColor: 200; 20; 55
          JrkScale: 0.019999999552965164
          JrkVis: false
          Name: Trajectory
          Num of samples: 100
          NumYaw: 20
          PosColor: 204; 51; 204
          PosScale: 0.10000000149011612
          Queue Size: 10
          Topic: /test_primitive/trajectory
          Unreliable: false
          Value: true
          VelColor: 85; 85; 255
          VelScale: 0.019999999552965164
          VelVis: false
          YawColor: 100; 20; 55
          YawScale: 0.05000000074505806
          YawTriangleAngle: 0.699999988079071
          YawTriangleScale: 0.5
          YawVis: false
      Enabled: true
      Name: GlobalPlanner
  Enabled: true
  Global Options:
    Background Color: 0; 0; 0
    Default Light: true
    Fixed Frame: world
    Frame Rate: 30
  Name: root
  Tools:
    - Class: rviz/Interact
      Hide Inactive Objects: true
    - Class: rviz/MoveCamera
    - Class: rviz/Select
    - Class: rviz/FocusCamera
    - Class: rviz/Measure
    - Class: rviz/SetInitialPose
      Theta std deviation: 0.2617993950843811
      Topic: /initialpose
      X std deviation: 0.5
      Y std deviation: 0.5
    - Class: rviz/SetGoal
      Topic: /move_base_simple/goal
    - Class: rviz/PublishPoint
      Single click: true
      Topic: /clicked_point
  Value: true
  Views:
    Current:
      Class: rviz/Orbit
      Distance: 28.32650375366211
      Enable Stereo Rendering:
        Stereo Eye Separation: 0.05999999865889549
        Stereo Focal Distance: 1
        Swap Stereo Eyes: false
        Value: false
      Field of View: 0.7853981852531433
      Focal Point:
        X: -0.9180400371551514
        Y: 19.10614013671875
        Z: 0.643409788608551
      Focal Shape Fixed Size: true
      Focal Shape Size: 0.05000000074505806
      Invert Z Axis: false
      Name: Current View
      Near Clip Distance: 0.009999999776482582
      Pitch: 0.12479695677757263
      Target Frame: world
      Yaw: 3.0918288230895996
    Saved: ~
Window Geometry:
  Displays:
    collapsed: false
  GT depth:
    collapsed: false
  Height: 1025
  Hide Left Dock: false
  Hide Right Dock: true
  QMainWindow State: 000000ff00000000fd00000004000000000000026000000363fc020000000cfb0000001200530065006c0065006300740069006f006e00000001e10000009b0000005c00fffffffb0000001e0054006f006f006c002000500072006f007000650072007400690065007302000001ed000001df00000185000000a3fb000000120056006900650077007300200054006f006f02000001df000002110000018500000122fb000000200054006f006f006c002000500072006f0070006500720074006900650073003203000002880000011d000002210000017afb000000100044006900730070006c006100790073010000003d0000017f000000c900fffffffb0000002000730065006c0065006300740069006f006e00200062007500660066006500720200000138000000aa0000023a00000294fb00000014005700690064006500530074006500720065006f02000000e6000000d2000003ee0000030bfb0000000c004b0069006e0065006300740200000186000001060000030c00000261fb0000001000470054002000640065007000740068000000016b000000d50000001600fffffffb0000001200530047004d00200064006500700074006800000002bd000000560000001600fffffffb0000000600520047004201000001c2000001de0000001600fffffffb0000000a0049006d00610067006501000002f7000000a90000000000000000000000010000010f00000363fc0200000003fb0000001e0054006f006f006c002000500072006f00700065007200740069006500730100000041000000780000000000000000fb0000000a00560069006500770073000000003d00000363000000a400fffffffb0000001200530065006c0065006300740069006f006e010000025a000000b200000000000000000000000200000490000000a9fc0100000001fb0000000a00560069006500770073030000004e00000080000002e10000019700000003000004380000003efc0100000002fb0000000800540069006d0065010000000000000438000002eb00fffffffb0000000800540069006d00650100000000000004500000000000000000000001d20000036300000004000000040000000800000008fc0000000100000002000000010000000a0054006f006f006c00730100000000ffffffff0000000000000000
  RGB:
    collapsed: false
  SGM depth:
    collapsed: false
  Selection:
    collapsed: false
  Time:
    collapsed: false
  Tool Properties:
    collapsed: false
  Views:
    collapsed: true
  Width: 1080
  X: 0
  Y: 0
