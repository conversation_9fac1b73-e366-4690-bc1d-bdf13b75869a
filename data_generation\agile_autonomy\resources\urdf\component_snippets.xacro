<?xml version="1.0"?>
<!--
  Copyright 2015 <PERSON><PERSON><PERSON>, ASL, ETH Zurich, Switzerland
  Copyright 2015 <PERSON>, ASL, ETH Zurich, Switzerland
  Copyright 2015 <PERSON>, ASL, ETH Zurich, Switzerland
  Copyright 2015 <PERSON><PERSON><PERSON>, ASL, ETH Zurich, Switzerland
  Copyright 2015 <PERSON>, ASL, ETH Zurich, Switzerland

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->

<robot xmlns:xacro="http://ros.org/wiki/xacro">
  <xacro:property name="pi" value="3.14159265359" />

  <!-- Macro to add logging to a bag file. -->
  <xacro:macro name="bag_plugin_macro" params="namespace bag_file rotor_velocity_slowdown_sim wait_to_record_bag">
    <gazebo>
      <plugin filename="librotors_gazebo_bag_plugin.so" name="rosbag">
        <robotNamespace>${namespace}</robotNamespace>
        <bagFileName>${bag_file}</bagFileName>
        <linkName>${namespace}/base_link</linkName>
        <frameId>${namespace}/base_link</frameId>
        <rotorVelocitySlowdownSim>${rotor_velocity_slowdown_sim}</rotorVelocitySlowdownSim>
        <waitToRecordBag>${wait_to_record_bag}</waitToRecordBag>
      </plugin>
    </gazebo>
  </xacro:macro>

  <!-- Macro to add a camera. -->
  <xacro:macro name="camera_macro" params="namespace parent_link camera_suffix frame_rate
      horizontal_fov image_width image_height image_format min_distance
      max_distance noise_mean noise_stddev enable_visual *geometry *origin">
    <link name="${namespace}/camera_${camera_suffix}_link">
      <collision>
        <origin xyz="0 0 0" rpy="0 0 0" />
        <geometry>
          <xacro:insert_block name="geometry" />
        </geometry>
      </collision>
      <xacro:if value="${enable_visual}">
        <visual>
          <origin xyz="0 0 0" rpy="0 ${pi/2} 0" />
          <geometry>
            <xacro:insert_block name="geometry" />
          </geometry>
          <material name="red" />
        </visual>
      </xacro:if>
      <inertial>
        <mass value="1e-5" />
        <origin xyz="0 0 0" rpy="0 0 0" />
        <inertia ixx="1e-6" ixy="0" ixz="0" iyy="1e-6" iyz="0" izz="1e-6" />
      </inertial>
    </link>
    <joint name="${namespace}/camera_${camera_suffix}_joint" type="fixed">
      <xacro:insert_block name="origin" />
      <parent link="${parent_link}" />
      <child link="${namespace}/camera_${camera_suffix}_link" />
    </joint>
    <link name="${namespace}/camera_${camera_suffix}_optical_link" />
    <joint name="${namespace}/camera_${camera_suffix}_optical_joint" type="fixed">
      <origin xyz="0 0 0" rpy="${-pi/2} 0 ${-pi/2}" />
      <parent link="${namespace}/camera_${camera_suffix}_link" />
      <child link="${namespace}/camera_${camera_suffix}_optical_link" />
    </joint>
    <gazebo reference="${namespace}/camera_${camera_suffix}_link">
      <sensor type="camera" name="${namespace}_camera_${camera_suffix}">
        <update_rate>${frame_rate}</update_rate>
        <camera name="head">
          <horizontal_fov>${horizontal_fov}</horizontal_fov>
          <image>
            <width>${image_width}</width>
            <height>${image_height}</height>
            <format>${image_format}</format>
          </image>
          <clip>
            <near>${min_distance}</near>
            <far>${max_distance}</far>
          </clip>
          <noise>
            <type>gaussian</type>
            <!-- Noise is sampled independently per pixel on each frame.
                 That pixel's noise value is added to each of its color
                 channels, which at that point lie in the range [0,1]. -->
            <mean>${noise_mean}</mean>
            <stddev>${noise_stddev}</stddev>
          </noise>
        </camera>
        <plugin name="${namespace}_camera_${camera_suffix}_controller" filename="libgazebo_ros_camera.so">
          <robotNamespace>${namespace}</robotNamespace>
          <alwaysOn>true</alwaysOn>
          <updateRate>${frame_rate}</updateRate>
          <cameraName>camera_${camera_suffix}</cameraName>
          <imageTopicName>image_raw</imageTopicName>
          <cameraInfoTopicName>camera_info</cameraInfoTopicName>
          <frameName>camera_${camera_suffix}_link</frameName>
          <hackBaseline>0.0</hackBaseline>
          <distortionK1>0.0</distortionK1>
          <distortionK2>0.0</distortionK2>
          <distortionK3>0.0</distortionK3>
          <distortionT1>0.0</distortionT1>
          <distortionT2>0.0</distortionT2>
        </plugin>
      </sensor>
    </gazebo>
  </xacro:macro>

  <!-- Macro to add a distorted camera. -->
  <xacro:macro name="camera_macro_distorted" params="namespace parent_link camera_suffix frame_rate
      horizontal_fov image_width image_height image_format min_distance
      max_distance noise_mean noise_stddev enable_visual enable_collision *geometry *origin">
    <link name="${namespace}/camera_${camera_suffix}_link">
      <xacro:if value="${enable_collision}">
        <collision>
          <origin xyz="0 0 0" rpy="0 0 0" />
          <geometry>
            <xacro:insert_block name="geometry" />
          </geometry>
        </collision>
      </xacro:if>
      <xacro:if value="${enable_visual}">
        <visual>
          <origin xyz="0 0 0" rpy="0 ${pi/2} 0" />
          <geometry>
            <xacro:insert_block name="geometry" />
          </geometry>
          <material name="red" />
        </visual>
      </xacro:if>
      <inertial>
        <mass value="1e-5" />
        <origin xyz="0 0 0" rpy="0 0 0" />
        <inertia ixx="1e-6" ixy="0" ixz="0" iyy="1e-6" iyz="0" izz="1e-6" />
      </inertial>
    </link>
    <joint name="${namespace}/camera_${camera_suffix}_joint" type="fixed">
      <xacro:insert_block name="origin" />
      <parent link="${parent_link}" />
      <child link="${namespace}/camera_${camera_suffix}_link" />
    </joint>
    <link name="${namespace}/camera_${camera_suffix}_optical_link" />
    <joint name="${namespace}/camera_${camera_suffix}_optical_joint" type="fixed">
      <origin xyz="0 0 0" rpy="${-pi/2} 0 ${-pi/2}" />
      <parent link="${namespace}/camera_${camera_suffix}_link" />
      <child link="${namespace}/camera_${camera_suffix}_optical_link" />
    </joint>
    <gazebo reference="${namespace}/camera_${camera_suffix}_link">
      <sensor type="camera" name="${namespace}_camera_${camera_suffix}">
        <update_rate>${frame_rate}</update_rate>
        <camera name="head">
          <horizontal_fov>${horizontal_fov}</horizontal_fov>
          <image>
            <width>${image_width}</width>
            <height>${image_height}</height>
            <format>${image_format}</format>
          </image>
          <clip>
            <near>${min_distance}</near>
            <far>${max_distance}</far>
          </clip>
          <noise>
            <type>gaussian</type>
            <!-- Noise is sampled independently per pixel on each frame.
                 That pixel's noise value is added to each of its color
                 channels, which at that point lie in the range [0,1]. -->
            <mean>${noise_mean}</mean>
            <stddev>${noise_stddev}</stddev>
          </noise>
        </camera>
        <plugin name="${namespace}_camera_${camera_suffix}_controller" filename="libgazebo_ros_camera.so">
          <robotNamespace>${namespace}</robotNamespace>
          <alwaysOn>true</alwaysOn>
          <updateRate>${frame_rate}</updateRate>
          <cameraName>camera_${camera_suffix}</cameraName>
          <imageTopicName>image_raw</imageTopicName>
          <cameraInfoTopicName>camera_info</cameraInfoTopicName>
          <frameName>camera_${camera_suffix}_link</frameName>
          <hackBaseline>0.0</hackBaseline>
          <!-- distortionK1>-0.19745014611866865</distortionK1>
          <distortionK2>0.0239638938451904</distortionK2>
          <distortionK3>0.0</distortionK3>
          <distortionT1>0.0018063222060033732</distortionT1>
          <distortionT2>-0.0027940453366193684</distortionT2 -->
          <distortionK1>0.0</distortionK1>
          <distortionK2>0.0</distortionK2>
          <distortionK3>0.0</distortionK3>
          <distortionT1>0.0</distortionT1>
          <distortionT2>0.0</distortionT2>
        </plugin>
      </sensor>
    </gazebo>
  </xacro:macro>

  <!-- Macro to add a depth camera on the VI-sensor. -->
  <xacro:macro name="depth_camera_macro" params="namespace parent_link camera_suffix frame_rate
      horizontal_fov image_width image_height image_format min_distance
      max_distance noise_mean noise_stddev enable_visual enable_collision *geometry *origin">
    <link name="${namespace}/camera_${camera_suffix}_link">
      <xacro:if value="${enable_collision}">
        <collision>
          <origin xyz="0 0 0" rpy="0 0 0" />
          <geometry>
            <xacro:insert_block name="geometry" />
          </geometry>
        </collision>
      </xacro:if>
      <xacro:if value="${enable_visual}">
        <visual>
          <origin xyz="0 0 0" rpy="0 ${pi/2} 0" />
          <geometry>
            <xacro:insert_block name="geometry" />
          </geometry>
          <material name="red" />
        </visual>
      </xacro:if>
      <inertial>
        <mass value="1e-5" />
        <origin xyz="0 0 0" rpy="0 0 0" />
        <inertia ixx="1e-6" ixy="0" ixz="0" iyy="1e-6" iyz="0" izz="1e-6" />
      </inertial>
    </link>
    <joint name="${namespace}/camera_${camera_suffix}_joint" type="fixed">
      <xacro:insert_block name="origin" />
      <parent link="${parent_link}" />
      <child link="${namespace}/camera_${camera_suffix}_link" />
      <limit upper="0" lower="0" effort="0" velocity="0" />
    </joint>
    <!-- Optical center of camera -->
    <link name="${namespace}/camera_${camera_suffix}_optical_center_link" />
    <joint name="${namespace}/camera_${camera_suffix}_optical_center_joint" type="fixed">
      <origin xyz="0 0 0" rpy="${-pi/2} 0 ${-pi/2}" />
      <parent link="${namespace}/camera_${camera_suffix}_link" />
      <child link="${namespace}/camera_${camera_suffix}_optical_center_link" />
      <limit upper="0" lower="0" effort="0" velocity="0" />
    </joint>
    <gazebo reference="${namespace}/camera_${camera_suffix}_link">
      <sensor type="depth" name="${namespace}_camera_{camera_suffix}">
        <always_on>true</always_on>
        <update_rate>${frame_rate}</update_rate>
        <camera>
          <horizontal_fov>${horizontal_fov}</horizontal_fov>
          <image>
            <width>${image_width}</width>
            <height>${image_height}</height>
            <format>L8</format>
          </image>
          <clip>
            <near>${min_distance}</near>
            <far>${max_distance}</far>
          </clip>
        </camera>
        <plugin name="${namespace}_camera_{camera_suffix}" filename="libgazebo_ros_openni_kinect.so">
          <robotNamespace>${namespace}</robotNamespace>
          <alwaysOn>true</alwaysOn>
          <baseline>0.11</baseline>
          <updateRate>${frame_rate}</updateRate>
          <cameraName>camera_${camera_suffix}</cameraName>
          <imageTopicName>camera/image_raw</imageTopicName>
          <cameraInfoTopicName>camera/camera_info</cameraInfoTopicName>
          <depthImageTopicName>depth/disparity</depthImageTopicName>
          <depthImageCameraInfoTopicName>depth/camera_info</depthImageCameraInfoTopicName>
          <pointCloudTopicName>depth/points</pointCloudTopicName>
          <frameName>camera_${camera_suffix}_optical_center_link</frameName>
          <pointCloudCutoff>${min_distance}</pointCloudCutoff>
          <pointCloudCutoffMax>${max_distance}</pointCloudCutoffMax>
          <distortionK1>0.0</distortionK1>
          <distortionK2>0.0</distortionK2>
          <distortionK3>0.0</distortionK3>
          <distortionT1>0.0</distortionT1>
          <distortionT2>0.0</distortionT2>
        </plugin>
      </sensor>
      <!-- <gazebo reference="${namespace}/camera_${camera_suffix}_link">
      <sensor name="camera" type="depth">
        <camera>
          <horizontal_fov>1.04719755</horizontal_fov>
          <image>
            <width>640</width>
            <height>480</height>
          </image>
          <clip>
            <near>0.1</near>
            <far>20</far>
          </clip>
          <depth_camera>
            <output>depths;points;normals</output>
          </depth_camera>
        </camera>
        <plugin filename="libDepthCameraPlugin.so" name="depth_camera_plugin">
          <CxPrime>0</CxPrime>
          <updateRate>10.0</updateRate>
          <cameraName>depth_cam1</cameraName>
          <frameName>/base_link</frameName>
          <hackBaseline>0.07</hackBaseline>
          <distortionK1>1.0</distortionK1>
          <distortionK2>1.0</distortionK2>
          <distortionK3>1.0</distortionK3>
          <distortionT1>1.0</distortionT1>
          <distortionT2>1.0</distortionT2>
        </plugin>
        <plugin name="kinect" filename="libgazebo_ros_openni_kinect.so">
          <CxPrime>0</CxPrime>
          <updateRate>10.0</updateRate>
          <imageTopicName>image_raw</imageTopicName>
          <pointCloudTopicName>points</pointCloudTopicName>
          <depthImageTopicName>image_depth</depthImageTopicName>
          <depthImageCameraInfoTopicName>depth_camera_info</depthImageCameraInfoTopicName>
          <pointCloudCutoff>0.001</pointCloudCutoff>
          <cameraName>kinect</cameraName>
          <frameName>/base_link</frameName>
          <distortionK1>0.0</distortionK1>
          <distortionK2>0.0</distortionK2>
          <distortionK3>0.0</distortionK3>
          <distortionT1>0.0</distortionT1>
          <distortionT2>0.0</distortionT2>
        </plugin>
        <always_on>true</always_on>
        <update_rate>10</update_rate>
      </sensor> -->
    </gazebo>
  </xacro:macro>

  <!-- Camera joint macro - just the joints, links, and collisions for a single
       camera. -->
  <xacro:macro name="camera_joint_macro" params="namespace parent_link camera_suffix enable_visual *origin *geometry">
    <link name="${namespace}/camera_${camera_suffix}_link">
      <collision>
        <origin xyz="0 0 0" rpy="0 0 0" />
        <geometry>
          <xacro:insert_block name="geometry" />
        </geometry>
      </collision>
      <xacro:if value="${enable_visual}">
        <visual>
          <origin xyz="0 0 0" rpy="0 0 0" />
          <geometry>
            <xacro:insert_block name="geometry" />
          </geometry>
          <material name="red" />
        </visual>
      </xacro:if>
      <inertial>
        <mass value="1e-5" />
        <origin xyz="0 0 0" rpy="0 0 0" />
        <inertia ixx="1e-6" ixy="0" ixz="0" iyy="1e-6" iyz="0" izz="1e-6" />
      </inertial>
    </link>
    <joint name="${namespace}/camera_${camera_suffix}_joint" type="fixed">
      <xacro:insert_block name="origin" />
      <parent link="${parent_link}" />
      <child link="${namespace}/camera_${camera_suffix}_link" />
    </joint>
  </xacro:macro>

  <!-- Camera sensor macro - just image parameters. -->
  <xacro:macro name="camera_sensor_macro" params="camera_suffix horizontal_fov image_width image_height
      image_format min_distance max_distance noise_mean noise_stddev
      baseline">
    <camera name="${camera_suffix}">
      <pose>0 ${-baseline} 0 0 0 0</pose>
      <horizontal_fov>${horizontal_fov}</horizontal_fov>
      <image>
        <width>${image_width}</width>
        <height>${image_height}</height>
        <format>${image_format}</format>
      </image>
      <clip>
        <near>${min_distance}</near>
        <far>${max_distance}</far>
      </clip>
      <noise>
        <type>gaussian</type>
        <!-- Noise is sampled independently per pixel on each frame.
             That pixel's noise value is added to each of its color
             channels, which at that point lie in the range [0,1]. -->
        <mean>${noise_mean}</mean>
        <stddev>${noise_stddev}</stddev>
      </noise>
    </camera>
  </xacro:macro>

  <!-- Macro to add a multicamera (stereo pair). -->
  <xacro:macro name="stereo_camera_macro" params="namespace camera_name parent_link frame_rate
      horizontal_fov image_width image_height image_format min_distance
      max_distance noise_mean noise_stddev enable_visual origin_offset_x
      baseline_y origin_offset_z *geometry">
    <!-- These are parameters for the left camera link and then the right
        as well. -->
    <xacro:camera_joint_macro namespace="${namespace}" parent_link="${parent_link}" camera_suffix="left" enable_visual="${enable_visual}">
      <origin xyz="${origin_offset_x} ${baseline_y/2} ${origin_offset_z}" rpy="0 0 0" />
      <xacro:insert_block name="geometry" />
    </xacro:camera_joint_macro>
    <xacro:camera_joint_macro namespace="${namespace}" parent_link="${parent_link}" camera_suffix="right" enable_visual="${enable_visual}">
      <origin xyz="${origin_offset_x} ${-baseline_y/2} ${origin_offset_z}" rpy="0 0 0" />
      <xacro:insert_block name="geometry" />
    </xacro:camera_joint_macro>

    <link name="${namespace}/camera_left_optical_link" />
    <joint name="${namespace}/camera_left_optical_joint" type="fixed">
      <origin xyz="0 0 0" rpy="${-pi/2} 0 ${-pi/2}" />
      <parent link="${namespace}/camera_left_link" />
      <child link="${namespace}/camera_left_optical_link" />
    </joint>

    <!-- Both cameras in the pair are anchored off the left camera frame. -->
    <gazebo reference="${namespace}/camera_left_link">
      <sensor type="multicamera" name="${namespace}_stereo_camera">
        <update_rate>${frame_rate}</update_rate>

        <!-- Here we set up the individual cameras of the stereo head. -->
        <xacro:camera_sensor_macro camera_suffix="left" horizontal_fov="${horizontal_fov}" image_width="${image_width}" image_height="${image_height}" image_format="${image_format}" min_distance="${min_distance}" max_distance="${max_distance}" noise_mean="${noise_mean}" noise_stddev="${noise_stddev}" baseline="0">
        </xacro:camera_sensor_macro>

        <xacro:camera_sensor_macro camera_suffix="right" horizontal_fov="${horizontal_fov}" image_width="${image_width}" image_height="${image_height}" image_format="${image_format}" min_distance="${min_distance}" max_distance="${max_distance}" noise_mean="${noise_mean}" noise_stddev="${noise_stddev}" baseline="${baseline_y}">
        </xacro:camera_sensor_macro>

        <!-- Stereo controller, setting the transforms between the two cameras. -->
        <plugin name="${namespace}_stereo_camera_controller" filename="libgazebo_ros_multicamera.so">
          <alwaysOn>true</alwaysOn>
          <updateRate>0.0</updateRate>
          <cameraName>${camera_name}</cameraName>
          <imageTopicName>image_raw</imageTopicName>
          <cameraInfoTopicName>camera_info</cameraInfoTopicName>
          <frameName>${camera_name}/camera_left_link</frameName>
          <hackBaseline>${baseline_y}</hackBaseline>
          <distortionK1>0.0</distortionK1>
          <distortionK2>0.0</distortionK2>
          <distortionK3>0.0</distortionK3>
          <distortionT1>0.0</distortionT1>
          <distortionT2>0.0</distortionT2>
        </plugin>
      </sensor>
    </gazebo>
  </xacro:macro>

  <!-- Macro to add the controller interface. -->
  <xacro:macro name="controller_plugin_macro" params="namespace imu_sub_topic">
    <gazebo>
      <plugin name="controller_interface" filename="librotors_gazebo_controller_interface.so">
        <robotNamespace>${namespace}</robotNamespace>
        <commandAttitudeThrustSubTopic>command/attitude</commandAttitudeThrustSubTopic>
        <commandRateThrustSubTopic>command/rate</commandRateThrustSubTopic>
        <commandMotorSpeedSubTopic>command/motor_speed</commandMotorSpeedSubTopic>
        <imuSubTopic>${imu_sub_topic}</imuSubTopic>
        <motorSpeedCommandPubTopic>gazebo/command/motor_speed</motorSpeedCommandPubTopic>
      </plugin>
    </gazebo>
  </xacro:macro>

  <!-- =============================================================== -->
  <!-- ==================== ROS INTERFACE MACRO ====================== -->
  <!-- =============================================================== -->
  <!-- <xacro:macro name="ros_interface_plugin_macro" params="namespace">
    <gazebo>
      <plugin name="ros_interface_plugin" filename="librotors_gazebo_ros_interface_plugin.so">
        <robotNamespace>${namespace}</robotNamespace>
      </plugin>
    </gazebo>
  </xacro:macro>-->

  <!-- =============================================================== -->
  <!-- ================== MAVLINK INTERFACE MACRO ==================== -->
  <!-- =============================================================== -->
  <xacro:macro name="mavlink_interface_macro" params="namespace mavlink_sub_topic imu_sub_topic mavlink_pub_topic 
      motors_speeds_pub_topic gps_update_freq rotor_count reference_magnetic_field_north 
      reference_magnetic_field_east reference_magnetic_field_down reference_latitude 
      reference_longitude reference_altitude">
    <gazebo>
      <plugin name="mavlink_interface" filename="librotors_gazebo_mavlink_interface.so">
        <robotNamespace>${namespace}</robotNamespace>
        <mavlinkControlSubTopic>${mavlink_sub_topic}</mavlinkControlSubTopic>
        <imuSubTopic>${imu_sub_topic}</imuSubTopic>
        <mavlinkHilSensorPubTopic>${mavlink_pub_topic}</mavlinkHilSensorPubTopic>
        <motorSpeedsPubTopic>${motors_speeds_pub_topic}</motorSpeedsPubTopic>
        <gpsUpdateFreq>${gps_update_freq}</gpsUpdateFreq>        <!-- Frequency of HIL GPS messages [Hz] -->
        <rotorCount>${rotor_count}</rotorCount>
        <!-- [Gauss] Below are the North, East, and Down components of the Earth's magnetic field at the reference location.
             The default reference location is Zurich (lat=+47.3667degN, lon=+8.5500degE, h=+500m, WGS84).
	     You can obtain the magnetic field strength for your location using the World Magnetic Model: 
             https://www.ngdc.noaa.gov/geomag/WMM/calculators.shtml -->
        <referenceMagNorth>${reference_magnetic_field_north}</referenceMagNorth>
        <referenceMagEast>${reference_magnetic_field_east}</referenceMagEast>
        <referenceMagDown>${reference_magnetic_field_down}</referenceMagDown>
        <referenceLatitude>${reference_latitude}</referenceLatitude>        <!-- the initial latitude [degrees [-90, 90]] -->
        <referenceLongitude>${reference_longitude}</referenceLongitude>        <!-- the initial longitude [degrees [-180, 180]] -->
        <referenceAltitude>${reference_altitude}</referenceAltitude>        <!-- the initial altitude [m] -->
      </plugin>
    </gazebo>
  </xacro:macro>

  <!-- Macro to add an IMU. -->
  <xacro:macro name="imu_plugin_macro" params="namespace imu_suffix parent_link imu_topic
      mass_imu_sensor gyroscope_noise_density gyroscope_random_walk
      gyroscope_bias_correlation_time gyroscope_turn_on_bias_sigma
      accelerometer_noise_density accelerometer_random_walk
      accelerometer_bias_correlation_time accelerometer_turn_on_bias_sigma
      *inertia *origin">
    <!-- IMU link -->
    <link name="${namespace}/imu${imu_suffix}_link">
      <inertial>
        <xacro:insert_block name="inertia" />
        <mass value="${mass_imu_sensor}" />
        <!-- [kg] -->
        <origin xyz="0 0 0" rpy="0 0 0" />
      </inertial>
    </link>
    <!-- IMU joint -->
    <joint name="${namespace}/imu${imu_suffix}_joint" type="revolute">
      <xacro:insert_block name="origin" />
      <parent link="${parent_link}" />
      <child link="${namespace}/imu${imu_suffix}_link" />
      <limit upper="0" lower="0" effort="0" velocity="0" />
    </joint>
    <gazebo>
      <plugin filename="librotors_gazebo_imu_plugin.so" name="rotors_gazebo_imu${imu_suffix}_plugin">
        <!-- A good description of the IMU parameters can be found in the kalibr documentation:
           https://github.com/ethz-asl/kalibr/wiki/IMU-Noise-Model-and-Intrinsics -->
        <robotNamespace>${namespace}</robotNamespace>        <!-- (string, required): ros namespace in which the messages are published -->
        <linkName>${namespace}/imu${imu_suffix}_link</linkName>        <!-- (string, required): name of the body which holds the IMU sensor -->
        <imuTopic>${imu_topic}</imuTopic>        <!-- (string): name of the sensor output topic and prefix of service names (defaults to imu) -->
        <gyroscopeNoiseDensity>${gyroscope_noise_density}</gyroscopeNoiseDensity>        <!-- Gyroscope noise density (two-sided spectrum) [rad/s/sqrt(Hz)] -->
        <gyroscopeRandomWalk>${gyroscope_random_walk}</gyroscopeRandomWalk>        <!-- Gyroscope bias random walk [rad/s/s/sqrt(Hz)] -->
        <gyroscopeBiasCorrelationTime>${gyroscope_bias_correlation_time}</gyroscopeBiasCorrelationTime>        <!-- Gyroscope bias correlation time constant [s] -->
        <gyroscopeTurnOnBiasSigma>${gyroscope_turn_on_bias_sigma}</gyroscopeTurnOnBiasSigma>        <!-- Gyroscope turn on bias standard deviation [rad/s] -->
        <accelerometerNoiseDensity>${accelerometer_noise_density}</accelerometerNoiseDensity>        <!-- Accelerometer noise density (two-sided spectrum) [m/s^2/sqrt(Hz)] -->
        <accelerometerRandomWalk>${accelerometer_random_walk}</accelerometerRandomWalk>        <!-- Accelerometer bias random walk. [m/s^2/s/sqrt(Hz)] -->
        <accelerometerBiasCorrelationTime>${accelerometer_bias_correlation_time}</accelerometerBiasCorrelationTime>        <!-- Accelerometer bias correlation time constant [s] -->
        <accelerometerTurnOnBiasSigma>${accelerometer_turn_on_bias_sigma}</accelerometerTurnOnBiasSigma>        <!-- Accelerometer turn on bias standard deviation [m/s^2] -->
      </plugin>
    </gazebo>
  </xacro:macro>

  <!-- Macro to add a generic odometry sensor. -->
  <xacro:macro name="odometry_plugin_macro" params="
      namespace odometry_sensor_suffix parent_link pose_topic pose_with_covariance_topic
      position_topic transform_topic odometry_topic parent_frame_id child_frame_id
      mass_odometry_sensor measurement_divisor measurement_delay unknown_delay
      noise_normal_position noise_normal_quaternion noise_normal_linear_velocity
      noise_normal_angular_velocity noise_uniform_position
      noise_uniform_quaternion noise_uniform_linear_velocity
      noise_uniform_angular_velocity enable_odometry_map odometry_map
      image_scale *inertia *origin">
    <!-- odometry link -->
    <link name="${namespace}/odometry_sensor${odometry_sensor_suffix}_link">
      <inertial>
        <xacro:insert_block name="inertia" />
        <mass value="${mass_odometry_sensor}" />
        <!-- [kg] -->
      </inertial>
    </link>
    <!-- odometry joint -->
    <joint name="${namespace}/odometry_sensor${odometry_sensor_suffix}_joint" type="revolute">
      <parent link="${parent_link}" />
      <xacro:insert_block name="origin" />
      <child link="${namespace}/odometry_sensor${odometry_sensor_suffix}_link" />
      <limit upper="0" lower="0" effort="0" velocity="0" />
    </joint>
    <gazebo>
      <plugin filename="librotors_gazebo_odometry_plugin.so" name="odometry_sensor${odometry_sensor_suffix}">
        <linkName>${namespace}/odometry_sensor${odometry_sensor_suffix}_link</linkName>
        <robotNamespace>${namespace}</robotNamespace>
        <poseTopic>${pose_topic}</poseTopic>
        <poseWithCovarianceTopic>${pose_with_covariance_topic}</poseWithCovarianceTopic>
        <positionTopic>${position_topic}</positionTopic>
        <transformTopic>${transform_topic}</transformTopic>
        <odometryTopic>${odometry_topic}</odometryTopic>
        <parentFrameId>${parent_frame_id}</parentFrameId>        <!-- Use the scoped link name here. e.g. Model::link. -->
        <childFrameId>${child_frame_id}</childFrameId>
        <measurementDivisor>${measurement_divisor}</measurementDivisor>        <!-- only every (seq % measurementDivisor) == 0 measurement is published [int] -->
        <measurementDelay>${measurement_delay}</measurementDelay>        <!-- time that measurement gets held back before it's published in [simulation cycles (int)] -->
        <unknownDelay>${unknown_delay}</unknownDelay>        <!-- additional delay, that just gets added to the timestamp [s] -->
        <noiseNormalPosition>${noise_normal_position}</noiseNormalPosition>        <!-- standard deviation of additive white gaussian noise [m] -->
        <noiseNormalQuaternion>${noise_normal_quaternion}</noiseNormalQuaternion>        <!-- standard deviation white gaussian noise [rad]: q_m = q*quaternionFromSmallAngleApproximation(noiseNormalQ) -->
        <noiseNormalLinearVelocity>${noise_normal_linear_velocity}</noiseNormalLinearVelocity>        <!-- standard deviation of additive white gaussian noise [m/s] -->
        <noiseNormalAngularVelocity>${noise_normal_angular_velocity}</noiseNormalAngularVelocity>        <!-- standard deviation of additive white gaussian noise [rad/s] -->
        <noiseUniformPosition>${noise_uniform_position}</noiseUniformPosition>        <!-- symmetric bounds of uniform noise [m] -->
        <noiseUniformQuaternion>${noise_uniform_quaternion}</noiseUniformQuaternion>        <!-- symmetric bounds of uniform noise [rad], computation see above -->
        <noiseUniformLinearVelocity>${noise_uniform_linear_velocity}</noiseUniformLinearVelocity>        <!-- symmetric bounds of uniform noise [m/s] -->
        <noiseUniformAngularVelocity>${noise_uniform_angular_velocity}</noiseUniformAngularVelocity>        <!-- symmetric bounds of uniform noise [rad/s] -->
        <xacro:if value="${enable_odometry_map}">
          <covarianceImage>package://rotors_gazebo/resource/${odometry_map}</covarianceImage>          <!-- a bitmap image describing where the sensor works (white), and where not (black) -->
          <covarianceImageScale>${image_scale}</covarianceImageScale>          <!-- the scale of the image in the gazebo world, if set to 1.0, 1 pixel in the image corresponds to 1 meter in the world -->
        </xacro:if>
      </plugin>
    </gazebo>
  </xacro:macro>

  <!-- Macro to add a GPS sensor. -->
  <xacro:macro name="gps_plugin_macro" params="namespace gps_suffix parent_link gps_topic ground_speed_topic
      mass_gps_sensor horizontal_pos_std_dev vertical_pos_std_dev
      horizontal_vel_std_dev vertical_vel_std_dev *inertia *origin">
    <!-- GPS link -->
    <link name="${namespace}/gps${gps_suffix}_link">
      <inertial>
        <xacro:insert_block name="inertia" />
        <mass value="${mass_gps_sensor}" />
        <!-- [kg] -->
        <origin xyz="0 0 0" rpy="0 0 0" />
      </inertial>
    </link>
    <!-- GPS joint -->
    <joint name="${namespace}/gps${gps_suffix}_joint" type="revolute">
      <xacro:insert_block name="origin" />
      <parent link="${parent_link}" />
      <child link="${namespace}/gps${gps_suffix}_link" />
      <limit upper="0" lower="0" effort="0" velocity="0" />
    </joint>
    <gazebo reference="${namespace}/gps${gps_suffix}_link">
      <sensor type="gps" name="${namespace}_gps${gps_suffix}">
        <pose>0 0 0 0 0 0</pose>
        <visualize>0</visualize>
        <always_on>1</always_on>
        <update_rate>5</update_rate>
        <gps>
          <position_sensing>
            <horizontal>
              <noise type="gaussian">
                <mean>0.0</mean>
                <stddev>${horizontal_pos_std_dev}</stddev>
                <bias_mean>0.0</bias_mean>
                <bias_stddev>0.0</bias_stddev>
              </noise>
            </horizontal>
            <vertical>
              <noise type="gaussian">
                <mean>0.0</mean>
                <stddev>${vertical_pos_std_dev}</stddev>
                <bias_mean>0.0</bias_mean>
                <bias_stddev>0.0</bias_stddev>
              </noise>
            </vertical>
          </position_sensing>
          <velocity_sensing>
            <horizontal>
              <noise type="gaussian">
                <mean>0.0</mean>
                <stddev>${horizontal_vel_std_dev}</stddev>
                <bias_mean>0.0</bias_mean>
                <bias_stddev>0.0</bias_stddev>
              </noise>
            </horizontal>
            <vertical>
              <noise type="gaussian">
                <mean>0.0</mean>
                <stddev>${vertical_vel_std_dev}</stddev>
                <bias_mean>0.0</bias_mean>
                <bias_stddev>0.0</bias_stddev>
              </noise>
            </vertical>
          </velocity_sensing>
        </gps>
        <plugin filename="librotors_gazebo_gps_plugin.so" name="rotors_gazebo_gps${gps_suffix}_plugin">
          <robotNamespace>${namespace}</robotNamespace>          <!-- (string, required): ros namespace in which the messages are published -->
          <linkName>${namespace}/gps${gps_suffix}_link</linkName>          <!-- (string, required): name of the body which holds the GPS sensor -->
          <gpsTopic>${gps_topic}</gpsTopic>          <!-- (string): name of the sensor output topic and prefix of service names (defaults to 'gps') -->
          <groundSpeedTopic>${ground_speed_topic}</groundSpeedTopic>          <!-- (string): name of the ground speed output topic (defaults to 'ground_speed') -->
          <horPosStdDev>${horizontal_pos_std_dev}</horPosStdDev>          <!-- standard deviation for horizontal position noise [m] -->
          <verPosStdDev>${vertical_pos_std_dev}</verPosStdDev>          <!-- standard deviation for vertical position noise [m] -->
          <horVelStdDev>${horizontal_vel_std_dev}</horVelStdDev>          <!-- standard deviation for horizontal speed noise [m/s] -->
          <verVelStdDev>${vertical_vel_std_dev}</verVelStdDev>          <!-- standard deviation for vertical speed noise [m/s] -->
        </plugin>
      </sensor>
    </gazebo>
  </xacro:macro>

  <!-- Macro to add a magnetometer. -->
  <xacro:macro name="magnetometer_plugin_macro" params="namespace magnetometer_suffix parent_link magnetometer_topic
      mass_magnetometer_sensor ref_mag_north ref_mag_east ref_mag_down 
      noise_normal noise_uniform_initial_bias *inertia *origin">
    <!-- Magnetometer link -->
    <link name="${namespace}/magnetometer${magnetometer_suffix}_link">
      <inertial>
        <xacro:insert_block name="inertia" />
        <mass value="${mass_magnetometer_sensor}" />
        <!-- [kg] -->
        <origin xyz="0 0 0" rpy="0 0 0" />
      </inertial>
    </link>
    <!-- Magnetometer joint -->
    <joint name="${namespace}/magnetometer${magnetometer_suffix}_joint" type="revolute">
      <xacro:insert_block name="origin" />
      <parent link="${parent_link}" />
      <child link="${namespace}/magnetometer${magnetometer_suffix}_link" />
      <limit upper="0" lower="0" effort="0" velocity="0" />
    </joint>
    <gazebo>
      <plugin filename="librotors_gazebo_magnetometer_plugin.so" name="rotors_gazebo_magnetometer${magnetometer_suffix}_plugin">
        <robotNamespace>${namespace}</robotNamespace>        <!-- (string, required): ros namespace in which the messages are published -->
        <linkName>${namespace}/magnetometer${magnetometer_suffix}_link</linkName>        <!-- (string, required): name of the body which holds the magnetometer -->
        <magnetometerTopic>${magnetometer_topic}</magnetometerTopic>        <!-- (string): name of the sensor output topic and prefix of service names (defaults to 'magnetic_field') -->
        <!-- [Tesla] Below is the reference Earth's magnetic field at the reference location in NED frame.
             The default reference location is Zurich (lat=+47.3667degN, lon=+8.5500degE, h=+500m, WGS84).
	     You can obtain the magnetic field strength for your location using the World Magnetic Model: 
             http://www.ngdc.noaa.gov/geomag-web/#igrfwmm -->
        <refMagNorth>${ref_mag_north}</refMagNorth>
        <refMagEast>${ref_mag_east}</refMagEast>
        <refMagDown>${ref_mag_down}</refMagDown>
        <noiseNormal>${noise_normal}</noiseNormal>        <!-- standard deviation of additive white gaussian noise [Tesla] -->
        <noiseUniformInitialBias>${noise_uniform_initial_bias}</noiseUniformInitialBias>        <!-- symmetric bounds of uniform noise for initial sensor bias [Tesla] -->
      </plugin>
    </gazebo>
  </xacro:macro>

  <!-- Macro to add an air pressure sensor. -->
  <xacro:macro name="pressure_sensor_plugin_macro" params="namespace pressure_sensor_suffix parent_link pressure_topic
      mass_pressure_sensor reference_altitude pressure_variance *inertia
      *origin">
    <!-- Pressure sensor link -->
    <link name="${namespace}/pressure_sensor${pressure_sensor_suffix}_link">
      <inertial>
        <xacro:insert_block name="inertia" />
        <mass value="${mass_pressure_sensor}" />
        <!-- [kg] -->
        <origin xyz="0 0 0" rpy="0 0 0" />
      </inertial>
    </link>
    <!-- Pressure sensor joint -->
    <joint name="${namespace}/pressure_sensor${pressure_sensor_suffix}_joint" type="revolute">
      <xacro:insert_block name="origin" />
      <parent link="${parent_link}" />
      <child link="${namespace}/pressure_sensor${pressure_sensor_suffix}_link" />
      <limit upper="0" lower="0" effort="0" velocity="0" />
    </joint>
    <gazebo>
      <plugin filename="librotors_gazebo_pressure_plugin.so" name="rotors_gazebo_pressure_sensor${pressure_sensor_suffix}_plugin">
        <robotNamespace>${namespace}</robotNamespace>        <!-- (string, required): ros namespace in which the messages are published -->
        <linkName>${namespace}/pressure_sensor${pressure_sensor_suffix}_link</linkName>        <!-- (string, required): name of the body which holds the pressure sensor -->
        <pressureTopic>${pressure_topic}</pressureTopic>        <!-- (string): name of the sensor output topic and prefix of service names (defaults to 'air_pressure') -->
        <referenceAltitude>${reference_altitude}</referenceAltitude>        <!-- the initial altitude [m] -->
        <pressureVariance>${pressure_variance}</pressureVariance>        <!-- the air pressure variance [Pa^2] -->
      </plugin>
    </gazebo>
  </xacro:macro>

  <!-- Macro to add the wind plugin. -->
  <xacro:macro name="wind_plugin_macro" params="namespace xyz_offset wind_direction wind_force_mean
      wind_gust_direction wind_gust_duration wind_gust_start
      wind_gust_force_mean wind_speed_mean use_custom_static_wind_field 
      custom_wind_field_path">
    <gazebo>
      <plugin filename="librotors_gazebo_wind_plugin.so" name="wind_plugin">
        <frameId>world</frameId>
        <linkName>${namespace}/base_link</linkName>
        <robotNamespace>${namespace}</robotNamespace>
        <xyzOffset>${xyz_offset}</xyzOffset>        <!-- [m] [m] [m] -->
        <windDirection>${wind_direction}</windDirection>
        <windForceMean>${wind_force_mean}</windForceMean>        <!-- [N] -->
        <windGustDirection>${wind_gust_direction}</windGustDirection>
        <windGustDuration>${wind_gust_duration}</windGustDuration>        <!-- [s] -->
        <windGustStart>${wind_gust_start}</windGustStart>        <!-- [s] -->
        <windGustForceMean>${wind_gust_force_mean}</windGustForceMean>        <!-- [N] -->
        <windSpeedMean>${wind_speed_mean}</windSpeedMean>        <!-- [m/s] -->
        <useCustomStaticWindField>${use_custom_static_wind_field}</useCustomStaticWindField>
        <customWindFieldPath>${custom_wind_field_path}</customWindFieldPath>        <!-- from ~/.ros -->
      </plugin>
    </gazebo>
  </xacro:macro>

  <!-- VI sensor macros -->
  <!-- Macro to add a VI-sensor camera. -->
  <xacro:macro name="vi_sensor_camera_macro" params="namespace parent_link camera_suffix frame_rate *origin">
    <xacro:camera_macro namespace="${namespace}" parent_link="${parent_link}" camera_suffix="${camera_suffix}" frame_rate="${frame_rate}" horizontal_fov="1.3962634" image_width="752" image_height="480" image_format="L8" min_distance="0.02" max_distance="30" noise_mean="0.0" noise_stddev="0.007" enable_visual="true">
      <mesh filename="package://rotors_description/meshes/vi_camera.dae" scale="1 1 1" />
      <xacro:insert_block name="origin" />
    </xacro:camera_macro>
  </xacro:macro>

  <!-- Macro to add a VI-sensor stereo camera. -->
  <xacro:macro name="vi_sensor_stereo_camera_macro" params="namespace parent_link frame_rate origin_offset_x baseline_y origin_offset_z max_range">
    <xacro:stereo_camera_macro namespace="${namespace}" camera_name="vi_sensor" parent_link="${parent_link}" frame_rate="${frame_rate}" horizontal_fov="1.3962634" image_width="752" image_height="480" image_format="L8" min_distance="0.02" max_distance="${max_range}" noise_mean="0.0" noise_stddev="0.007" enable_visual="false" origin_offset_x="${origin_offset_x}" baseline_y="${baseline_y}" origin_offset_z="${origin_offset_z}">
      <cylinder length="0.01" radius="0.007" />
    </xacro:stereo_camera_macro>
  </xacro:macro>

  <!-- Macro to add a depth camera on the VI-sensor. -->
  <xacro:macro name="vi_sensor_depth_macro" params="namespace parent_link camera_suffix frame_rate max_range *origin">
    <link name="${namespace}/camera_${camera_suffix}_link">
      <collision>
        <origin xyz="0 0 0" rpy="0 0 0" />
        <geometry>
          <cylinder length="0.01" radius="0.007" />
        </geometry>
      </collision>
      <inertial>
        <mass value="1e-5" />
        <origin xyz="0 0 0" rpy="0 0 0" />
        <inertia ixx="1e-6" ixy="0" ixz="0" iyy="1e-6" iyz="0" izz="1e-6" />
      </inertial>
    </link>
    <joint name="${namespace}/camera_${camera_suffix}_joint" type="fixed">
      <xacro:insert_block name="origin" />
      <parent link="${parent_link}" />
      <child link="${namespace}/camera_${camera_suffix}_link" />
      <limit upper="0" lower="0" effort="0" velocity="0" />
    </joint>
    <!-- Optical center of camera -->
    <link name="${namespace}/camera_${camera_suffix}_optical_center_link" />
    <joint name="${namespace}/camera_${camera_suffix}_optical_center_joint" type="fixed">
      <origin xyz="0 0 0" rpy="${-pi/2} 0 ${-pi/2}" />
      <parent link="${namespace}/camera_${camera_suffix}_link" />
      <child link="${namespace}/camera_${camera_suffix}_optical_center_link" />
      <limit upper="0" lower="0" effort="0" velocity="0" />
    </joint>
    <gazebo reference="${namespace}/camera_${camera_suffix}_link">
      <sensor type="depth" name="${namespace}_camera_{camera_suffix}">
        <always_on>true</always_on>
        <update_rate>${frame_rate}</update_rate>
        <camera>
          <horizontal_fov>2</horizontal_fov>
          <image>
            <format>L8</format>
            <width>640</width>
            <height>480</height>
          </image>
          <clip>
            <near>0.01</near>
            <far>${max_range}</far>
          </clip>
        </camera>
        <plugin name="${namespace}_camera_{camera_suffix}" filename="libgazebo_ros_openni_kinect.so">
          <robotNamespace>${namespace}</robotNamespace>
          <alwaysOn>true</alwaysOn>
          <baseline>0.11</baseline>
          <updateRate>${frame_rate}</updateRate>
          <cameraName>camera_${camera_suffix}</cameraName>
          <imageTopicName>camera/image_raw</imageTopicName>
          <cameraInfoTopicName>camera/camera_info</cameraInfoTopicName>
          <depthImageTopicName>depth/disparity</depthImageTopicName>
          <depthImageCameraInfoTopicName>depth/camera_info</depthImageCameraInfoTopicName>
          <pointCloudTopicName>depth/points</pointCloudTopicName>
          <frameName>camera_${camera_suffix}_optical_center_link</frameName>
          <pointCloudCutoff>0.5</pointCloudCutoff>
          <pointCloudCutoffMax>${max_range}</pointCloudCutoffMax>
          <distortionK1>0.0</distortionK1>
          <distortionK2>0.0</distortionK2>
          <distortionK3>0.0</distortionK3>
          <distortionT1>0.0</distortionT1>
          <distortionT2>0.0</distortionT2>
        </plugin>
      </sensor>
    </gazebo>
  </xacro:macro>

  <!-- VI-Sensor Macro -->
  <xacro:macro name="vi_sensor_macro" params="namespace parent_link enable_cameras enable_depth enable_ground_truth *origin">
    <!-- Vi Sensor Link -->
    <link name="${namespace}/vi_sensor_link">
      <collision>
        <origin xyz="0 0 0" rpy="0 0 0" />
        <geometry>
          <box size="0.03 0.133 0.057" />
        </geometry>
      </collision>

      <visual>
        <origin xyz="0 0 0" rpy="0 0 0" />
        <geometry>
          <mesh filename="package://rotors_description/meshes/vi_sensor.dae" scale="1 1 1" />
        </geometry>
      </visual>

      <inertial>
        <mass value="0.13" />
        <origin xyz="0 0 0" rpy="0 0 0" />
        <inertia ixx="1e-6" ixy="0" ixz="0" iyy="1e-6" iyz="0" izz="1e-6" />
      </inertial>
    </link>
    <joint name="${namespace}_vi_sensor_joint" type="fixed">
      <xacro:insert_block name="origin" />
      <parent link="${parent_link}" />
      <child link="${namespace}/vi_sensor_link" />
    </joint>
    <!-- Cameras -->
    <xacro:if value="${enable_cameras}">
      <!-- Insert stereo pair. -->
      <xacro:vi_sensor_stereo_camera_macro namespace="${namespace}" parent_link="${namespace}/vi_sensor_link" frame_rate="30.0" origin_offset_x="0.015" baseline_y="${0.055*2}" origin_offset_z="0.0065" max_range="30.0">
      </xacro:vi_sensor_stereo_camera_macro>
    </xacro:if>

    <!-- Depth Sensor -->
    <xacro:if value="${enable_depth}">
      <xacro:vi_sensor_depth_macro namespace="${namespace}" parent_link="${namespace}/vi_sensor_link" camera_suffix="depth" frame_rate="30.0" max_range="10.0">
        <origin xyz="0.015 0.055 0.0065" rpy="0 0 0" />
      </xacro:vi_sensor_depth_macro>
    </xacro:if>

    <!-- Groundtruth -->
    <xacro:if value="${enable_ground_truth}">
      <!-- Odometry Sensor -->
      <xacro:odometry_plugin_macro namespace="${namespace}/ground_truth" odometry_sensor_suffix="" parent_link="${namespace}/vi_sensor_link" pose_topic="pose" pose_with_covariance_topic="pose_with_covariance" position_topic="position" transform_topic="transform" odometry_topic="odometry" parent_frame_id="world" child_frame_id="${namespace}/base_link" mass_odometry_sensor="0.00001" measurement_divisor="1" measurement_delay="0" unknown_delay="0.0" noise_normal_position="0 0 0" noise_normal_quaternion="0 0 0" noise_normal_linear_velocity="0 0 0" noise_normal_angular_velocity="0 0 0" noise_uniform_position="0 0 0" noise_uniform_quaternion="0 0 0" noise_uniform_linear_velocity="0 0 0" noise_uniform_angular_velocity="0 0 0" enable_odometry_map="false" odometry_map="" image_scale="">
        <inertia ixx="0.00001" ixy="0.0" ixz="0.0" iyy="0.00001" iyz="0.0" izz="0.00001" />
        <!-- [kg m^2] [kg m^2] [kg m^2] [kg m^2] [kg m^2] [kg m^2] -->
        <origin xyz="0.0 0.0 0.0" rpy="0.0 0.0 0.0" />
      </xacro:odometry_plugin_macro>
    </xacro:if>

    <!-- ADIS16448 IMU. -->
    <xacro:imu_plugin_macro namespace="${namespace}" imu_suffix="" parent_link="${namespace}/vi_sensor_link" imu_topic="imu" mass_imu_sensor="0.015" gyroscope_noise_density="0.0003394" gyroscope_random_walk="0.000038785" gyroscope_bias_correlation_time="1000.0" gyroscope_turn_on_bias_sigma="0.0087" accelerometer_noise_density="0.004" accelerometer_random_walk="0.006" accelerometer_bias_correlation_time="300.0" accelerometer_turn_on_bias_sigma="0.1960">
      <inertia ixx="0.00001" ixy="0.0" ixz="0.0" iyy="0.00001" iyz="0.0" izz="0.00001" />
      <origin xyz="0.015 0 0.0113" rpy="0 0 0" />
    </xacro:imu_plugin_macro>
  </xacro:macro>

  <xacro:macro name="ground_truth_imu_and_odometry" params="namespace parent_link">
    <!-- Mount an IMU providing ground truth. -->
    <xacro:imu_plugin_macro namespace="${namespace}" imu_suffix="gt" parent_link="${parent_link}" imu_topic="ground_truth/imu" mass_imu_sensor="0.00001" gyroscope_noise_density="0.0" gyroscope_random_walk="0.0" gyroscope_bias_correlation_time="1000.0" gyroscope_turn_on_bias_sigma="0.0" accelerometer_noise_density="0.0" accelerometer_random_walk="0.0" accelerometer_bias_correlation_time="300.0" accelerometer_turn_on_bias_sigma="0.0">
      <inertia ixx="0.00001" ixy="0.0" ixz="0.0" iyy="0.00001" iyz="0.0" izz="0.00001" />
      <origin xyz="0 0 0" rpy="0 0 0" />
    </xacro:imu_plugin_macro>

    <!-- Mount a generic odometry sensor providing ground truth. -->
    <xacro:odometry_plugin_macro namespace="${namespace}" odometry_sensor_suffix="gt" parent_link="${parent_link}" pose_topic="ground_truth/pose" pose_with_covariance_topic="ground_truth/pose_with_covariance" position_topic="ground_truth/position" transform_topic="ground_truth/transform" odometry_topic="ground_truth/odometry" parent_frame_id="world" child_frame_id="${namespace}/base_link" mass_odometry_sensor="0.00001" measurement_divisor="1" measurement_delay="0" unknown_delay="0.0" noise_normal_position="0 0 0" noise_normal_quaternion="0 0 0" noise_normal_linear_velocity="0 0 0" noise_normal_angular_velocity="0 0 0" noise_uniform_position="0 0 0" noise_uniform_quaternion="0 0 0" noise_uniform_linear_velocity="0 0 0" noise_uniform_angular_velocity="0 0 0" enable_odometry_map="false" odometry_map="" image_scale="">
      <inertia ixx="0.00001" ixy="0.0" ixz="0.0" iyy="0.00001" iyz="0.0" izz="0.00001" />
      <!-- [kg m^2] [kg m^2] [kg m^2] [kg m^2] [kg m^2] [kg m^2] -->
      <origin xyz="0.0 0.0 0.0" rpy="0.0 0.0 0.0" />
    </xacro:odometry_plugin_macro>
  </xacro:macro>

  <xacro:macro name="default_imu" params="namespace parent_link">
    <!-- ADIS16448 IMU. -->
    <xacro:imu_plugin_macro namespace="${namespace}" imu_suffix="" parent_link="${parent_link}" imu_topic="imu" mass_imu_sensor="0.015" gyroscope_noise_density="0.0003394" gyroscope_random_walk="0.000038785" gyroscope_bias_correlation_time="1000.0" gyroscope_turn_on_bias_sigma="0.0087" accelerometer_noise_density="0.004" accelerometer_random_walk="0.006" accelerometer_bias_correlation_time="300.0" accelerometer_turn_on_bias_sigma="0.1960">
      <inertia ixx="0.00001" ixy="0.0" ixz="0.0" iyy="0.00001" iyz="0.0" izz="0.00001" />
      <origin xyz="0 0 0" rpy="0 0 0" />
    </xacro:imu_plugin_macro>
  </xacro:macro>

  <xacro:macro name="perfect_imu" params="namespace parent_link">
    <!-- PERFECT IMU. -->
    <xacro:imu_plugin_macro namespace="${namespace}" imu_suffix="" parent_link="${parent_link}" imu_topic="imu" mass_imu_sensor="0.015" gyroscope_noise_density="0.000" gyroscope_random_walk="0.000" gyroscope_bias_correlation_time="1000.0" gyroscope_turn_on_bias_sigma="0.000" accelerometer_noise_density="0.000" accelerometer_random_walk="0.000" accelerometer_bias_correlation_time="300.0" accelerometer_turn_on_bias_sigma="0.0">
      <inertia ixx="0.00001" ixy="0.0" ixz="0.0" iyy="0.00001" iyz="0.0" izz="0.00001" />
      <origin xyz="0 0 0" rpy="0 0 0" />
    </xacro:imu_plugin_macro>
  </xacro:macro>

  <xacro:macro name="default_gps" params="namespace parent_link">
    <!-- Default GPS. -->
    <xacro:gps_plugin_macro namespace="${namespace}" gps_suffix="" parent_link="${parent_link}" gps_topic="gps" ground_speed_topic="ground_speed" mass_gps_sensor="0.015" horizontal_pos_std_dev="3.0" vertical_pos_std_dev="6.0" horizontal_vel_std_dev="0.1" vertical_vel_std_dev="0.1">
      <inertia ixx="0.00001" ixy="0.0" ixz="0.0" iyy="0.00001" iyz="0.0" izz="0.00001" />
      <origin xyz="0 0 0" rpy="0 0 0" />
    </xacro:gps_plugin_macro>
  </xacro:macro>

  <xacro:macro name="default_magnetometer" params="namespace parent_link">
    <!-- ADIS16448 Magnetometer. -->
    <xacro:magnetometer_plugin_macro namespace="${namespace}" magnetometer_suffix="" parent_link="${parent_link}" magnetometer_topic="magnetic_field" mass_magnetometer_sensor="0.015" ref_mag_north="0.000021493" ref_mag_east="0.000000815" ref_mag_down="0.000042795" noise_normal="0.000000080 0.000000080 0.000000080" noise_uniform_initial_bias="0.000000400 0.000000400 0.000000400">
      <inertia ixx="0.00001" ixy="0.0" ixz="0.0" iyy="0.00001" iyz="0.0" izz="0.00001" />
      <origin xyz="0 0 0" rpy="0 0 0" />
    </xacro:magnetometer_plugin_macro>
  </xacro:macro>

  <xacro:macro name="default_pressure_sensor" params="namespace parent_link">
    <xacro:pressure_sensor_plugin_macro namespace="${namespace}" pressure_sensor_suffix="" parent_link="${parent_link}" pressure_topic="air_pressure" mass_pressure_sensor="0.015" reference_altitude="500.0" pressure_variance="0.0">
      <inertia ixx="0.00001" ixy="0.0" ixz="0.0" iyy="0.00001" iyz="0.0" izz="0.00001" />
      <origin xyz="0 0 0" rpy="0 0 0" />
    </xacro:pressure_sensor_plugin_macro>
  </xacro:macro>

  <xacro:macro name="default_mavlink_interface" params="namespace imu_sub_topic rotor_count">
    <xacro:mavlink_interface_macro namespace="${namespace}" mavlink_sub_topic="/mavlink/from" imu_sub_topic="${imu_sub_topic}" mavlink_pub_topic="/mavlink/to" motors_speeds_pub_topic="gazebo/command/motor_speed" gps_update_freq="5.0" rotor_count="${rotor_count}" reference_magnetic_field_north="0.21475" reference_magnetic_field_east="0.00797" reference_magnetic_field_down="0.42817" reference_latitude="47.3667" reference_longitude="8.5500" reference_altitude="500.0">
    </xacro:mavlink_interface_macro>
  </xacro:macro>

</robot>
