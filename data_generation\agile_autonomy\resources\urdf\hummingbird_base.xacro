<?xml version="1.0"?>
<!--
  Copyright 2015 <PERSON><PERSON><PERSON>, ASL, ETH Zurich, Switzerland
  Copyright 2015 <PERSON>, ASL, ETH Zurich, Switzerland
  Copyright 2015 <PERSON>, ASL, ETH Zurich, Switzerland
  Copyright 2015 <PERSON><PERSON><PERSON>, ASL, ETH Zurich, Switzerland
  Copyright 2015 <PERSON>, ASL, ETH Zurich, Switzerland

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->

<robot name="hummingbird" xmlns:xacro="http://ros.org/wiki/xacro">
  <!-- xacro:include filename="$(find rotors_description)/urdf/component_snippets.xacro" / -->
  <xacro:include filename="$(find agile_autonomy)/resources/urdf/component_snippets.xacro" />
  <!-- Instantiate hummingbird "mechanics" -->
<!--  <xacro:include filename="$(find rotors_description)/urdf/hummingbird.xacro" />-->
  <xacro:include filename="$(find agile_autonomy)/resources/urdf/hummingbird.xacro" />

  <!-- Instantiate a controller. -->
  <xacro:controller_plugin_macro namespace="${namespace}" imu_sub_topic="imu" />

  <xacro:if value="$(arg enable_mavlink_interface)">
    <!-- Instantiate mavlink telemetry interface. -->
    <xacro:default_mavlink_interface namespace="${namespace}" imu_sub_topic="imu" rotor_count="4" />
  </xacro:if>

  <!-- Mount an ADIS16448 IMU. -->
  <xacro:default_imu namespace="${namespace}" parent_link="${namespace}/base_link" />

  <xacro:if value="$(arg enable_ground_truth)">
    <xacro:ground_truth_imu_and_odometry namespace="${namespace}" parent_link="${namespace}/base_link" />
  </xacro:if>

  <xacro:if value="$(arg enable_logging)">
    <!-- Instantiate a logger -->
    <xacro:bag_plugin_macro
      namespace="${namespace}"
      bag_file="$(arg log_file)"
      rotor_velocity_slowdown_sim="${rotor_velocity_slowdown_sim}"
      wait_to_record_bag="$(arg wait_to_record_bag)" />
  </xacro:if>

</robot>

