<?xml version="1.0"?>
<sdf version="1.6">
  <world name="simple_world">

    <plugin name="ros_interface_plugin" filename="librotors_gazebo_ros_interface_plugin.so"></plugin>

    <physics type="ode">
      <max_step_size>0.001</max_step_size>
      <real_time_factor>0.5</real_time_factor>
      <real_time_update_rate>500</real_time_update_rate>
    </physics>

    <include>
      <uri>model://sun</uri>
    </include>

    <gui fullscreen="0">
      <camera name="user_camera">
        <pose>-20.0 10 20.0 0 0.5 0</pose>
        <view_controller>orbit</view_controller>
      </camera>
    </gui>

    <model name="ground_plane">
      <pose>-60.0 -60.0 0 0 0 0</pose>
      <static>true</static>
      <link name="link">
        <collision name="collision">
          <geometry>
            <plane>
              <normal>0 0 1</normal>
              <size>120 120</size>
            </plane>
          </geometry>
          <surface>
            <friction>
              <ode>
                <mu>100</mu>
                <mu2>50</mu2>
              </ode>
            </friction>
          </surface>
        </collision>
        <visual name="visual">
          <cast_shadows>false</cast_shadows>
          <geometry>
            <plane>
              <normal>0 0 1</normal>
              <size>120 120</size>
            </plane>
          </geometry>
        </visual>
      </link>
    </model>

    <state world_name='simple_world'>
      <sim_time>0 0</sim_time>
      <real_time>0 0</real_time>
      <wall_time>1510043711 945147391</wall_time>
      <iterations>0</iterations>


      <model name='ground_plane'>
        <pose frame=''>0 0 0 0 -0 0</pose>
        <scale>1 1 1</scale>
        <link name='link'>
          <pose frame=''>0 0 0 0 -0 0</pose>
          <velocity>0 0 0 0 -0 0</velocity>
          <acceleration>0 0 0 0 -0 0</acceleration>
          <wrench>0 0 0 0 -0 0</wrench>
        </link>
      </model>

      <light name='sun'>
        <pose frame=''>0 0 10 0 -0 0</pose>
      </light>
    </state>

  </world>
</sdf>