/***********************************************************************
  Copyright (C) 2020 <PERSON><PERSON><PERSON>moto

  This program is free software: you can redistribute it and/or modify
  it under the terms of the GNU General Public License as published by
  the Free Software Foundation, either version 3 of the License, or
  (at your option) any later version.
 
  This program is distributed in the hope that it will be useful,
  but WITHOUT ANY WARRANTY; without even the implied warranty of
  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
  GNU General Public License for more details.
  You should have received a copy of the GNU General Public License
  along with this program.  If not, see <http://www.gnu.org/licenses/>.
***********************************************************************/

#include "sgm_gpu_node.h"
#include <ros/ros.h>

int main(int argc, char** argv)
{
  ros::init(argc, argv, "sgm_gpu_node");
  sgm_gpu::SgmGpuNode sgm_gpu;
  ros::spin();

  return 0;
}

