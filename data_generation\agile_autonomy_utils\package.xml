<?xml version="1.0"?>
<package format="2">
  <name>agile_autonomy_utils</name>
  <version>0.0.0</version>
  <description>Utils for agile autonomy project</description>

  <maintainer email="e<PERSON><PERSON><PERSON>@ifi.uzh.ch"><PERSON><PERSON></maintainer>

  <license>GNU GPL</license>

  <buildtool_depend>catkin</buildtool_depend>
  <buildtool_depend>catkin_simple</buildtool_depend>


  <depend>autopilot</depend>
  <depend>eigen_catkin</depend>
  <depend>geometry_msgs</depend>
  <depend>quadrotor_common</depend>
  <depend>roscpp</depend>
  <depend>rpg_common</depend>
  <depend>std_msgs</depend>
  <depend>trajectory_generation_helper</depend>
  <depend>trajectory_msgs</depend>

  <export>
  </export>
</package>
