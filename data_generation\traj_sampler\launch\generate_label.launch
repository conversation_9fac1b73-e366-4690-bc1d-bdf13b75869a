<launch>
    <arg name="quad_name" default="hummingbird"/>

    <group ns="$(arg quad_name)">
        <!-- Label Generation -->
        <arg name="data_dir" default="$(find agile_autonomy)/../data"/>
        <node pkg="traj_sampler" type="generate_label" name="generate_label"
              output="screen"> <!--  launch-prefix="gdb -ex run - -args" -->
            <param name="data_dir" value="$(arg data_dir)"/>
            <param name="n_threads" value="1"/>
            <param name="thread_idx" value="0"/>
            <rosparam file="$(find traj_sampler)/parameters/mpc_params.yaml"/>
            <rosparam file="$(find traj_sampler)/parameters/label_generation.yaml"/>
        </node>
    </group>
</launch>
