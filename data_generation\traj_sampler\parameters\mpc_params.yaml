# Cost on states
Q_pos_xy:     200   # Cost for horizontal positon error
Q_pos_z:      500   # Cost for vertical position error
Q_attitude:   20    # Cost for attitude error
Q_velocity:   10    # Cost for velocity error
Q_perception: 0     # Cost for reprojection error to camera optical axis (PAMPC)

# Cost on Inputs
R_thrust:     1.0     # Cost on thrust input
R_pitchroll:  1.0     # Cost on pitch and roll rate
R_yaw:        1.0     # Cost on yaw rate

# Exponential scaling: W_i = W * exp(-i/N * cost_scaling).
# cost_scaling = 0 means no scaling W_i = W.
state_cost_exponential: 0.0     # Scaling for state costs
input_cost_exponential: 0.0     # scaling for input costs

# Limits for inputs
max_bodyrate_xy:    5.0       # ~ pi [rad/s]
max_bodyrate_z:     2.0         # ~ pi*2/3 [rad/s]
min_thrust:         2.0       # ~ 20% gravity [N]
max_thrust:         15.0      # ~ 200% gravity [N]

# Extrinsics for Perception Aware MPC
p_B_C:      [ 0.0, 0.0, 0.0 ]               # camera in body center [m]
q_B_C:      [ 0.6427876, 0, 0.7660444, 0 ]  # 45° pitched down

# Print information such as timing to terminal
print_info:       false
