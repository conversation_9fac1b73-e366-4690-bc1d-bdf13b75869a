repositories:
  catkin_boost_python_buildtool:
    type: git
    url: **************:ethz-asl/catkin_boost_python_buildtool.git
    version: master
  catkin_simple:
    type: git
    url: **************:catkin/catkin_simple.git
    version: master
  eigen_catkin:
    type: git
    url: **************:ethz-asl/eigen_catkin.git
    version: master
  eigen_checks:
    type: git
    url: **************:ethz-asl/eigen_checks.git
    version: master
  gflags_catkin:
    type: git
    url: **************:ethz-asl/gflags_catkin.git
    version: master
  glog_catkin:
    type: git
    url: **************:ethz-asl/glog_catkin.git
    version: master
  mav_comm:
    type: git
    url: **************:ethz-asl/mav_comm.git
    version: master
  minimum_jerk_trajectories:
    type: git
    url: **************:uzh-rpg/minimum_jerk_trajectories.git
    version: master
  minkindr:
    type: git
    url: **************:ethz-asl/minkindr.git
    version: master
  minkindr_ros:
    type: git
    url: **************:ethz-asl/minkindr_ros.git
    version: master
  numpy_eigen:
    type: git
    url: **************:ethz-asl/numpy_eigen.git
    version: master
  rpg_common:
    type: git
    url: **************:kelia/rpg_common.git
    version: main
  rotors_simulator:
    type: git
    url: **************:ethz-asl/rotors_simulator.git
    version: master
  rpg_mpc:
    type: git
    url: **************:uzh-rpg/rpg_mpc.git
    version: feature/return_full_horizon
  rpg_quadrotor_common:
    type: git
    url: **************:uzh-rpg/rpg_quadrotor_common.git
    version: master
  rpg_quadrotor_control:
    type: git
    url: **************:uzh-rpg/rpg_quadrotor_control.git
    version: devel/elia
  rpg_single_board_io:
    type: git
    url: **************:uzh-rpg/rpg_single_board_io.git
    version: master
  rpg_flightmare:
    type: git
    url: **************:antonilo/flightmare_agile_autonomy.git
    version: main
  rpg_mpl_ros:
    type: git
    url: **************:uzh-rpg/rpg_mpl_ros.git
    version: master
  assimp_catkin:
    type: git
    url: **************:uzh-rpg/assimp_catkin.git
    version: master
