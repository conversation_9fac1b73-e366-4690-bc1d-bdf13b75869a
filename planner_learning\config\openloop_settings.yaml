log_dir: '/tmp/nw_predictions'
quad_name: 'none'
odometry_topic: 'ground_truth/odometry'
rgb_topic: 'agile_autonomy/unity_rgb'
depth_topic: 'agile_autonomy/sgm_depth'
use_rgb: False # Use camera images
use_depth: True # Use depth images
state_dim: 3 # x,y,z
out_seq_len: 10 # Number of steps in the future to predict
predict_state_number: [] # Empty list means will predict all states in out_seq_len. Otherwise it will train only for the single state. Only supported mode is len(predict_state_number) == 1.
poly_coeff: 3 # Equivalent to poly order minus 1
seq_len: 1 # Number of steps in the past to load
img_width: 224
img_height: 224
modes: 3 # only supported 2
checkpoint:
  resume_training: True
  resume_file: "model/ckpt-1"
test:
  batch_size: 1 # should be one!
  top_trajectories: 1
  data_save_freq: 15
  test_dir: "data/train_all_7ms/test"
  mode: "prediction"
  ref_frame: 'bf'
  train_on_global_traj: False
inputs:
  position: False
  attitude: True
  bodyrates: True
  velocity_frame: 'bf'
  pitch_angle: 0
  hack_velocity_ref: True
