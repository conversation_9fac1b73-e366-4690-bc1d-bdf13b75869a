quad_name: 'hummingbird'
odometry_topic: 'ground_truth/odometry'
rgb_topic: 'agile_autonomy/unity_rgb'
depth_topic: 'agile_autonomy/sgm_depth'
log_dir: '/tmp/trash_reactive'
verbose: False
use_rgb: False # Use camera images
use_depth: True # Use depth images
state_dim: 3 # x,y,z
out_seq_len: 10 # Number of steps in the future to predict
future_time: 5.0 # in seconds, how much in the future to track the reference
predict_state_number: [] # Empty list means will predict all states in out_seq_len. Otherwise it will train only for the single state. Only supported mode is len(predict_state_number) == 1.
seq_len: 1 # History Len
modes: 3 # Number of Trajectories Predicted
ref_frame: "bf"
img_width: 224
img_height: 224
checkpoint:
  resume_training: True
  resume_file: "models/ckpt-50"
test_time:
  max_rollouts: 50
  test_folder: "/tmp/test"
  expert_folder: "../data_generation/data/"
  fallback_radius_expert: 0.0 #mt unused at test time
  network_frequency: 15.0 #Hz
  input_update_freq: 15 #Hz
  execute_nw_predictions: True
  perform_inference: True # Completely disable network forward passes if False
  crashed_thr: 0.18
  accept_thresh: 0.9 # when ratio btw other and best score is larger than this, other trajectory will be sent as well.
  spacings: [5] # mt, 5,6,7, etc. for forest, 6,7,8, etc. for objects.
  track_global_traj: False # Do not use this at test time!
inputs:
  position: False
  attitude: True
  bodyrates: True
  velocity_frame: 'bf'
  pitch_angle: 0
unity:
  random_seed: 0
  # x,y,z, yaw
  # forest
  unity_start_pos: [[-20.,20.,0.,0]]
