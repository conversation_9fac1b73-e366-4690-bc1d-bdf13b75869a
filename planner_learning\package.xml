<?xml version="1.0"?>
<package format="2">
  <name>planner_learning</name>
  <version>0.0.0</version>
  <description>The planner_learning package</description>

  <maintainer email="lo<PERSON><PERSON>@ifi.uzh.ch"><PERSON></maintainer>
  <license>MIT</license>

  <author><PERSON></author>

  <buildtool_depend>catkin</buildtool_depend>
  <buildtool_depend>catkin_simple</buildtool_depend>

  <depend>autopilot</depend>
  <depend>eigen_catkin</depend>
  <depend>minimum_jerk_trajectories</depend>
  <depend>agile_autonomy_msgs</depend>
  <depend>quadrotor_common</depend>
  <depend>quadrotor_msgs</depend>
  <depend>roscpp</depend>
  <depend>std_msgs</depend>
  <depend>nav_msgs</depend>

  <export>

  </export>
</package>
